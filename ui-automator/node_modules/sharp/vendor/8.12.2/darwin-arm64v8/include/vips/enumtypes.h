
/* This file is generated by glib-mkenums, do not modify it. This code is licensed under the same license as the containing project. Note that it links to GLib, so must comply with the LGPL linking clauses. */

#ifndef VIPS_ENUM_TYPES_H
#define VIPS_ENUM_TYPES_H

G_BEGIN_DECLS
/* enumerations from "../../../libvips/include/vips/arithmetic.h" */
GType vips_operation_math_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_OPERATION_MATH (vips_operation_math_get_type())
GType vips_operation_math2_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_OPERATION_MATH2 (vips_operation_math2_get_type())
GType vips_operation_round_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_OPERATION_ROUND (vips_operation_round_get_type())
GType vips_operation_relational_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_OPERATION_RELATIONAL (vips_operation_relational_get_type())
GType vips_operation_boolean_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_OPERATION_BOOLEAN (vips_operation_boolean_get_type())
GType vips_operation_complex_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_OPERATION_COMPLEX (vips_operation_complex_get_type())
GType vips_operation_complex2_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_OPERATION_COMPLEX2 (vips_operation_complex2_get_type())
GType vips_operation_complexget_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_OPERATION_COMPLEXGET (vips_operation_complexget_get_type())
/* enumerations from "../../../libvips/include/vips/basic.h" */
GType vips_precision_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_PRECISION (vips_precision_get_type())
/* enumerations from "../../../libvips/include/vips/colour.h" */
GType vips_intent_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_INTENT (vips_intent_get_type())
GType vips_pcs_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_PCS (vips_pcs_get_type())
/* enumerations from "../../../libvips/include/vips/conversion.h" */
GType vips_extend_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_EXTEND (vips_extend_get_type())
GType vips_compass_direction_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_COMPASS_DIRECTION (vips_compass_direction_get_type())
GType vips_direction_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_DIRECTION (vips_direction_get_type())
GType vips_align_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_ALIGN (vips_align_get_type())
GType vips_angle_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_ANGLE (vips_angle_get_type())
GType vips_angle45_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_ANGLE45 (vips_angle45_get_type())
GType vips_interesting_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_INTERESTING (vips_interesting_get_type())
GType vips_blend_mode_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_BLEND_MODE (vips_blend_mode_get_type())
/* enumerations from "../../../libvips/include/vips/convolution.h" */
GType vips_combine_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_COMBINE (vips_combine_get_type())
/* enumerations from "../../../libvips/include/vips/draw.h" */
GType vips_combine_mode_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_COMBINE_MODE (vips_combine_mode_get_type())
/* enumerations from "../../../libvips/include/vips/foreign.h" */
GType vips_foreign_flags_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_FLAGS (vips_foreign_flags_get_type())
GType vips_fail_on_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FAIL_ON (vips_fail_on_get_type())
GType vips_saveable_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_SAVEABLE (vips_saveable_get_type())
GType vips_foreign_subsample_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_SUBSAMPLE (vips_foreign_subsample_get_type())
GType vips_foreign_jpeg_subsample_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_JPEG_SUBSAMPLE (vips_foreign_jpeg_subsample_get_type())
GType vips_foreign_webp_preset_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_WEBP_PRESET (vips_foreign_webp_preset_get_type())
GType vips_foreign_tiff_compression_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_TIFF_COMPRESSION (vips_foreign_tiff_compression_get_type())
GType vips_foreign_tiff_predictor_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_TIFF_PREDICTOR (vips_foreign_tiff_predictor_get_type())
GType vips_foreign_tiff_resunit_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_TIFF_RESUNIT (vips_foreign_tiff_resunit_get_type())
GType vips_foreign_png_filter_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_PNG_FILTER (vips_foreign_png_filter_get_type())
GType vips_foreign_ppm_format_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_PPM_FORMAT (vips_foreign_ppm_format_get_type())
GType vips_foreign_dz_layout_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_DZ_LAYOUT (vips_foreign_dz_layout_get_type())
GType vips_foreign_dz_depth_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_DZ_DEPTH (vips_foreign_dz_depth_get_type())
GType vips_foreign_dz_container_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_DZ_CONTAINER (vips_foreign_dz_container_get_type())
GType vips_foreign_heif_compression_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_FOREIGN_HEIF_COMPRESSION (vips_foreign_heif_compression_get_type())
/* enumerations from "../../../libvips/include/vips/image.h" */
GType vips_demand_style_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_DEMAND_STYLE (vips_demand_style_get_type())
GType vips_image_type_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_IMAGE_TYPE (vips_image_type_get_type())
GType vips_interpretation_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_INTERPRETATION (vips_interpretation_get_type())
GType vips_band_format_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_BAND_FORMAT (vips_band_format_get_type())
GType vips_coding_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_CODING (vips_coding_get_type())
GType vips_access_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_ACCESS (vips_access_get_type())
/* enumerations from "../../../libvips/include/vips/morphology.h" */
GType vips_operation_morphology_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_OPERATION_MORPHOLOGY (vips_operation_morphology_get_type())
/* enumerations from "../../../libvips/include/vips/object.h" */
GType vips_argument_flags_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_ARGUMENT_FLAGS (vips_argument_flags_get_type())
/* enumerations from "../../../libvips/include/vips/operation.h" */
GType vips_operation_flags_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_OPERATION_FLAGS (vips_operation_flags_get_type())
/* enumerations from "../../../libvips/include/vips/region.h" */
GType vips_region_shrink_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_REGION_SHRINK (vips_region_shrink_get_type())
/* enumerations from "../../../libvips/include/vips/resample.h" */
GType vips_kernel_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_KERNEL (vips_kernel_get_type())
GType vips_size_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_SIZE (vips_size_get_type())
/* enumerations from "../../../libvips/include/vips/util.h" */
GType vips_token_get_type (void) G_GNUC_CONST;
#define VIPS_TYPE_TOKEN (vips_token_get_type())
G_END_DECLS

#endif /*VIPS_ENUM_TYPES_H*/

/* Generated data ends here */

