/**
 * @file speed-analyse.jsx 速度评测主模板
 * @create 王家麒@2022.06.06
 */
import { Tabs } from 'antd';
import { FileTextOutlined, MonitorOutlined, SnippetsOutlined } from '@ant-design/icons';

import TaskInfo from './task-info/index';
import FrameCorrect from './frame-correct/index';
import ReportDownload from './report-download/index';

export default ({ dispatch, state }) => {
    const { TabPane } = Tabs;
    return (
        <Tabs
            defaultActiveKey="1"
            centered
            activeKey={state.editor.perfAssess.operator.speedAnalyse.activeTab}
            onTabClick={(key) => {
                switch (key) {
                    case '1':
                        dispatch({
                            type: 'editor/setSpeedAnalyse',
                            payload: {
                                mode: 1,
                                // 创建任务弹窗用
                                taskModalOpen: false,
                                createTaskOption: {
                                    taskName: null,
                                    deviceType: null,
                                    deviceId: null,
                                    expectedTimes: 1,
                                    caseList: []
                                },
                                // 任务列表
                                taskList: [],
                                // 处理后的任务展示
                                tableList: [],
                                // 追加执行用
                                appendTimes: 0,
                                sortIndexList: [], // 报告数据排序
                                reportTitle: '', // 报告标题
                                reportNames: [], // 报告小标题
                                reportTime: 0,
                                reportDataArray: [], // 报告数据列表
                                reportInfoList: [], // 报告筛选数据列表
                                reportDescList: [], // 报告描述列表
                                reportTableDataList: [] // 报告数据表列表
                            }
                        });
                        dispatch({ type: 'editor/getSpeedTaskList' });
                        break;
                    case '2':
                        let taskList = state.editor.perfAssess.operator.speedAnalyse.taskList;
                        let taskId = null;
                        let caseId = null;
                        let sceneId = null;
                        for (let taskItem of taskList) {
                            if (
                                taskItem.actualTimes - taskItem.exceptionTimes > 0
                                && 0 === taskItem.isValid
                                && 2 === taskItem.taskStatus
                            ) {
                                taskId = taskItem.taskId;
                                caseId = taskItem.caseList[0].caseId;
                                sceneId = taskItem.caseList[0].sceneList[0].id;
                                break;
                            }
                        }
                        dispatch({
                            type: 'editor/setSpeedAnalyse',
                            payload: {
                                // 检查模式用
                                mode: 1,
                                checkMode: false,
                                // 分帧任务选择用
                                correctTaskOption: {
                                    taskId,
                                    caseId,
                                    sceneId
                                },
                                correctList: [],
                                correctTotalCount: 0,
                                correctTablePage: 0,
                                // 校准弹窗用
                                firstPage: 0,
                                lastPage: 0,
                                previewIndex: -1,
                                sortIndexList: [], // 报告数据排序
                                reportTitle: '', // 报告标题
                                reportNames: [], // 报告小标题
                                reportTime: 0,
                                reportDataArray: [], // 报告数据列表
                                reportInfoList: [], // 报告筛选数据列表
                                reportDescList: [], // 报告描述列表
                                reportTableDataList: [] // 报告数据表列表
                            }
                        });
                        dispatch({
                            type: 'editor/getSpeedCorrectList',
                            payload: {
                                state,
                                dispatch,
                                page: 0,
                                index: 0
                            }
                        });
                        break;
                    case '3':
                        dispatch({ type: 'editor/getSpeedTaskList' });
                        dispatch({
                            type: 'editor/setSpeedAnalyse',
                            payload: {
                                // 报告展示用
                                reportInfoSelectDict: {
                                    type: 'task',
                                    taskId: [],
                                    deviceId: null
                                },
                                reportData: {},
                                sortIndexList: [], // 报告数据排序
                                reportTitle: '', // 报告标题
                                reportNames: [], // 报告小标题
                                reportTime: 0,
                                reportDataArray: [], // 报告数据列表
                                reportInfoList: [], // 报告筛选数据列表
                                reportDescList: [], // 报告描述列表
                                reportTableDataList: [] // 报告数据表列表
                            }
                        });
                        break;
                    default: break;
                }
                dispatch({
                    type: 'editor/setSpeedAnalyse',
                    payload: {
                        activeTab: key
                    }
                });
            }}
        >
            <TabPane
                tab={
                    <span>
                        <FileTextOutlined />
                        任务详情
                    </span>
                }
                key="1"
            >
                <TaskInfo dispatch={dispatch} state={state} />
            </TabPane>
            <TabPane
                tab={
                    <span>
                        <MonitorOutlined />
                        分帧校准
                    </span>
                }
                key="2"
            >
                <FrameCorrect dispatch={dispatch} state={state} />
            </TabPane>
            <TabPane
                tab={
                    <span>
                        <SnippetsOutlined />
                        2
                    </span>
                }
                key="3"
            >
                <ReportDownload dispatch={dispatch} state={state} />
            </TabPane>
        </Tabs>
    );
};
