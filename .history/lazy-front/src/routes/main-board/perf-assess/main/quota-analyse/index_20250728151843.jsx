/**
 * @file quota-analyse.jsx 指标评测主模板
 * @create 王家麒@2022.07.11
 */
import { Tabs } from 'antd';
import { BarChartOutlined, UnorderedListOutlined, SnippetsOutlined } from '@ant-design/icons';

import QuotaList from './quota-list/index';
import QuotaReport from './quota-report/index';
import ReportDownload from './report-download';

export default ({ dispatch, state }) => {
    const { TabPane } = Tabs;
    return (
        <Tabs
            defaultActiveKey="1"
            centered
            activeKey={state.editor.perfAssess.operator.quotaAnalyse.activeTab}
            onTabClick={(key) => {
                switch (key) {
                    case '1':
                        dispatch({
                            type: 'editor/setQuotaAnalyse',
                            payload: {
                                // 创建任务弹窗用
                                createTaskOption: {
                                    taskName: null,
                                    deviceType: null,
                                    deviceId: null,
                                    expectedTimes: 1,
                                    caseList: []
                                },
                                // 任务列表
                                taskList: [],
                                // 处理后的任务展示
                                tableList: [],
                            }
                        });
                        dispatch({ type: 'editor/getQuotaTaskList' });
                        break;
                    case '2':
                        let taskList = state.editor.perfAssess.operator.quotaAnalyse.taskList;
                        let taskId = null;
                        let caseId = null;
                        let sceneId = null;
                        for (let taskItem of taskList) {
                            if (
                                taskItem.actualTimes - taskItem.exceptionTimes > 0
                                && 0 === taskItem.isValid
                                && 2 === taskItem.taskStatus
                            ) {
                                taskId = taskItem.taskId;
                                caseId = taskItem.caseList[0].caseId;
                                sceneId = taskItem.caseList[0].sceneList[0].sceneId;
                                break;
                            }
                        }
                        dispatch({
                            type: 'editor/setQuotaAnalyse',
                            payload: {
                                quotaTaskOption: {
                                    taskId,
                                    caseId,
                                    sceneId
                                },
                                quotaTaskList: [],
                                quotaTaskIndex: -1
                            }
                        });
                        dispatch({
                            type: 'editor/getQuotaWholeInfo',
                            payload: {
                                getReportOutput: false
                            }
                        });
                        break;
                    default: break;
                }
                dispatch({
                    type: 'editor/setQuotaAnalyse',
                    payload: {
                        activeTab: key,
                        taskModalOpen: false
                    }
                });
            }}
        >
            <TabPane
                tab={
                    <span>
                        <UnorderedListOutlined />
                        任务列表
                    </span>
                }
                key="1"
            >
                <QuotaList dispatch={dispatch} state={state} />
            </TabPane>
            <TabPane
                tab={
                    <span>
                        <BarChartOutlined />
                        执行报告
                    </span>
                }
                key="2"
            >
                <QuotaReport dispatch={dispatch} state={state} />
            </TabPane>
            <TabPane
                // disabled
                tab={
                    <span>
                        <SnippetsOutlined />
                        报告产出
                    </span>
                }
                key="3"
            >
                <ReportDownload dispatch={dispatch} state={state} />
            </TabPane>
        </Tabs>
    );
};
