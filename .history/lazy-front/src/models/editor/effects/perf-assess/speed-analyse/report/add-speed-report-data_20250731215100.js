/**
 * @file add-report-data.js 添加报告内容测计划
 * @create 李爽@2022.09.22
 */
import { message } from 'antd';
import electron from '../../../../../../utils/electron';

export default {
    * addSpeedReportData({ payload: { dataIndex } }, { put, select, call }) {
        try {
            let {
                speedAnalyse: { reportTaskOption, reportDataArray, reportTableDataList, reportInfoList }
            } = yield select(state => state.editor.perfAssess.operator);

            if (null === reportTaskOption.taskId) {
                message.warning('任务不得为空');
                return;
            }
            if (null === reportTaskOption.caseId) {
                message.warning('用例不得为空');
                return;
            }
            if (null === reportTaskOption.sceneId) {
                message.warning('场景不得为空');
                return;
            }
            if (null === reportTaskOption.modalIndex) {
                message.warning(' 报告模块不得为空');
                return;
            }

            // 检查完整性
            let dataArray = [];
            for (let infoItem of reportInfoList[dataIndex]) {
                let { dataList } = yield call(electron.send, 'assess.task.report.detail', infoItem);
                dataArray.push(dataList[0])
            }
            let newInfo = {
                taskId: reportTaskOption.taskId,
                caseId: reportTaskOption.caseId,
                sceneId: reportTaskOption.sceneId
            };
            let { dataList } = yield call(electron.send, 'assess.task.report.detail', newInfo);
            dataArray.push(dataList[0])
            reportInfoList[dataIndex].push(newInfo);
            // 处理报告数据
            let reportData = {
                min: 1000000,
                max: -1,
                keyPoint: {
                    min: [],
                    max: [],
                    avg: []
                },
                chartData: {
                    placeholder: [],
                    minToLeft: [],
                    leftToAvg: [],
                    avgToRight: [],
                    rightToMax: []
                },
                titleName: [],
                sum: []
            };
            // 处理图表需要的数据
            let reportTableDataItem = [];
            for (let dataItem of dataArray) {
                let reportDataItem = {};
                if (0 === dataItem.dataInfo.length) {
                    continue;
                }
                // 计算所有数据的最大最小值
                reportData.max = Math.max(dataItem.dataInfo.afterGrubbs.max, reportData.max);
                reportData.min = Math.min(dataItem.dataInfo.afterGrubbs.min, reportData.min);

                // 获取当前数据的最大最小平均值
                reportData.keyPoint.max.push(dataItem.dataInfo.afterGrubbs.max);
                reportData.keyPoint.min.push(dataItem.dataInfo.afterGrubbs.min);
                reportData.keyPoint.avg.push(Math.floor(dataItem.dataInfo.afterGrubbs.average));

                reportDataItem.max = dataItem.dataInfo.afterGrubbs.max;
                reportDataItem.min = dataItem.dataInfo.afterGrubbs.min;
                reportDataItem.avg = dataItem.dataInfo.afterGrubbs.average;
                // 获取当前数据的左右置信区间
                let leftSem =
                    Math.floor(dataItem.dataInfo.afterGrubbs.average - 2 * dataItem.dataInfo.afterGrubbs.sem);
                if (leftSem < dataItem.dataInfo.afterGrubbs.min) {
                    leftSem = dataItem.dataInfo.afterGrubbs.min;
                }
                let rightSem =
                    Math.floor(dataItem.dataInfo.afterGrubbs.average + 2 * dataItem.dataInfo.afterGrubbs.sem);
                if (rightSem > dataItem.dataInfo.afterGrubbs.max) {
                    rightSem = dataItem.dataInfo.afterGrubbs.max;
                }
                // 获取当前数据的可区间长度
                reportData.chartData.placeholder.push(dataItem.dataInfo.afterGrubbs.min);
                reportData.chartData.minToLeft.push(leftSem - dataItem.dataInfo.afterGrubbs.min);
                reportData.chartData.leftToAvg.push(dataItem.dataInfo.afterGrubbs.average - leftSem);
                reportData.chartData.avgToRight.push(rightSem - dataItem.dataInfo.afterGrubbs.average);
                reportData.chartData.rightToMax.push(dataItem.dataInfo.afterGrubbs.max - rightSem);
                // 获取当前数据总数
                reportData.sum.push(
                    `sum: ${!dataItem.dataInfo.afterGrubbs.count ? '-' : dataItem.dataInfo.afterGrubbs.count}` +
                    ` / ${!dataItem.dataInfo.beforeGrubbs.count ? '-' : dataItem.dataInfo.beforeGrubbs.count}`
                );

                reportData.titleName.push(`${dataItem.suiteName} - ${dataItem.caseName} # ${dataItem.sceneName}`);
                reportDataItem.caseName = dataItem.caseName;
                reportDataItem.suiteName = dataItem.suiteName;
                reportDataItem.sceneName = dataItem.sceneName;
                reportTableDataItem.push(reportDataItem);
            }
            // 更新前端
            if (-1 === reportData.max) {
                message.error('无报告数据');
            }
            else {
                yield put({
                    type: 'setSpeedAnalyse',
                    payload: {
                        reportData
                    }
                });
                reportDataArray[dataIndex] = reportData;
                reportTableDataList[dataIndex] = reportTableDataItem;
                yield put({
                    type: 'setSpeedAnalyse',
                    payload: {
                        reportDataArray,
                        reportTableDataList,
                        reportInfoList
                    }
                });
            }
        } catch (err) {
            message.error(err.message);
        }
    }
};